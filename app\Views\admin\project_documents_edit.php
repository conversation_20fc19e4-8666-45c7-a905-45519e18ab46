<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
    ← Back to Documents
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            ✏️ Edit Project Document
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update document details for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Form -->
<div style="max-width: 800px; margin: 0 auto;">
    <div class="card">
        <div class="card-header">
            ✏️ Edit Document Details
        </div>
        <div style="padding: var(--spacing-xl);">
            <?php if (session()->getFlashdata('errors')): ?>
                <div style="background: var(--bg-danger); color: var(--text-danger); padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                    <ul style="margin: 0; padding-left: var(--spacing-lg);">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div style="background: var(--bg-danger); color: var(--text-danger); padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                    <?= esc(session()->getFlashdata('error')) ?>
                </div>
            <?php endif; ?>

            <!-- Current File Info -->
            <div style="background: var(--bg-info); color: var(--text-info); padding: var(--spacing-md); border-radius: var(--radius-md); border-left: 4px solid var(--brand-info); margin-bottom: var(--spacing-lg);">
                <h6 style="margin-bottom: var(--spacing-sm); display: flex; align-items: center; gap: var(--spacing-xs);">
                    📄 Current File Information
                </h6>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                    <div>
                        <strong>File Path:</strong> <?= esc(basename($document['doc_path'])) ?><br>
                        <strong>Version:</strong> v<?= $document['version_no'] ?? 1 ?>
                    </div>
                    <div>
                        <strong>Upload Date:</strong> <?= date('M j, Y g:i A', strtotime($document['created_at'])) ?><br>
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>"
                           class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                            📥 Download Current File
                        </a>
                    </div>
                </div>
            </div>

            <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/edit') ?>" enctype="multipart/form-data">
                <?= csrf_field() ?>

                <div style="display: grid; gap: var(--spacing-lg);">
                    <!-- Document Category -->
                    <div>
                        <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">
                            Document Category
                        </label>
                        <select name="category" id="category"
                                class="form-input">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $key => $label): ?>
                                <option value="<?= $key ?>" <?= (old('category', $document['doc_type']) == $key) ? 'selected' : '' ?>>
                                    <?= esc($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                            Choose the appropriate document category (optional)
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="3" maxlength="255"
                                  class="form-input" style="resize: vertical;"><?= old('description', $document['description'] ?? '') ?></textarea>
                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                            Optional description or notes about the document (max 255 characters)
                        </div>
                    </div>

                    <!-- Replace Document File -->
                    <div>
                        <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">
                            Replace Document File
                        </label>
                        <input type="file" id="document_file" name="document_file"
                               class="form-input">
                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                            <strong>Optional:</strong> Select a new file to replace the current document. Leave empty to keep the current file.<br>
                            <strong>File Requirements:</strong>
                            <ul style="margin: var(--spacing-xs) 0 0 var(--spacing-lg); padding: 0;">
                                <li>Maximum file size: 25MB</li>
                                <li>Allowed file types: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF, ODT, ODS, ODP, JPG, JPEG, PNG, GIF, BMP, TIFF, SVG, ZIP, RAR, 7Z, TAR, GZ, CSV, XML, JSON</li>
                                <li>Executable files (.exe, .bat) and application files are not permitted</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Warning Alert -->
                    <div style="background: var(--bg-warning); color: var(--text-warning); padding: var(--spacing-md); border-radius: var(--radius-md); border-left: 4px solid var(--brand-warning);">
                        <div style="display: flex; align-items: flex-start; gap: var(--spacing-sm);">
                            <div style="font-size: 1.2rem;">⚠️</div>
                            <div>
                                <strong>Note:</strong> If you upload a new file, it will permanently replace the current file.
                                The old file will be deleted from the server and cannot be recovered.
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div style="display: flex; justify-content: space-between; gap: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
                            ❌ Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            💾 Update Document
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// File size validation
document.getElementById('document_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const maxSize = 25 * 1024 * 1024; // 25MB in bytes
        if (file.size > maxSize) {
            alert('File size exceeds 25MB limit. Please choose a smaller file.');
            e.target.value = '';
            return;
        }
        
        // Display file info
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.remove();
        }
        
        const info = document.createElement('div');
        info.id = 'fileInfo';
        info.className = 'mt-2 p-2 bg-light rounded';
        info.innerHTML = `
            <small>
                <strong>New file selected:</strong> ${file.name}<br>
                <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                <strong>Type:</strong> ${file.type || 'Unknown'}
            </small>
        `;
        e.target.parentNode.appendChild(info);
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
});
</script>

<style>
/* Color-coded Form Input System */
.form-input,
select.form-input,
input.form-input,
textarea.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.95rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid #10B981 !important; /* Green for optional */
    transition: border-color 0.2s;
}
.form-input[required],
select.form-input[required],
input.form-input[required],
textarea.form-input[required] {
    border: 2px solid #EF4444 !important; /* Red for required */
}
.form-input:focus {
    border-color: #059669 !important;
    box-shadow: 0 0 0 3px rgba(16,185,129,0.1);
}
.form-input[required]:focus {
    border-color: #DC2626 !important;
    box-shadow: 0 0 0 3px rgba(239,68,68,0.1);
}
.form-input:hover {
    border-color: #059669 !important;
}
.form-input[required]:hover {
    border-color: #DC2626 !important;
}
input[type="file"].form-input {
    cursor: pointer;
}
input[type="file"].form-input::-webkit-file-upload-button {
    background: var(--brand-primary);
    color: white;
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    margin-right: var(--spacing-sm);
    cursor: pointer;
}
@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-md) !important;
        font-size: 0.875rem !important;
    }
    .card {
        margin: var(--spacing-sm);
    }
    div[style*="padding: var(--spacing-xl)"] {
        padding: var(--spacing-lg) !important;
    }
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }
}
</style>
<?= $this->endSection() ?>
