<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#budget-items" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="btn btn-primary">
    💰 Add Budget Item
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Budget
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Budget items for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Budget Statistics -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
    <!-- Total Budget -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">💰</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
            $<?= number_format($budgetStats['total_planned'], 2) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Total Planned Budget</div>
    </div>

    <!-- Total Items -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📊</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            <?= $budgetStats['total_items'] ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Budget Items</div>
    </div>

    <!-- Active Items -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">✅</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            <?= $budgetStats['by_status']['active'] ?? 0 ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Active Items</div>
    </div>

    <!-- Removed Items -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">❌</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-danger); margin-bottom: var(--spacing-xs);">
            <?= $budgetStats['by_status']['removed'] ?? 0 ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Removed Items</div>
    </div>
</div>

<!-- Budget Items Table -->
<div class="card">
    <div class="card-header">
        💰 Budget Items
    </div>

    <?php if (!empty($budgetItems)): ?>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: var(--bg-tertiary); border-bottom: 1px solid var(--border-color);">
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            Item Code
                        </th>
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            Description
                        </th>
                        <th style="padding: var(--spacing-md); text-align: right; font-weight: 600; color: var(--text-primary);">
                            Planned Amount
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Status
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Created
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($budgetItems as $item): ?>
                        <tr style="border-bottom: 1px solid var(--border-color);">
                            <td style="padding: var(--spacing-md);">
                                <div style="font-weight: 600; color: var(--text-primary);">
                                    <?= esc($item['item_code']) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md);">
                                <div style="color: var(--text-primary);">
                                    <?= esc($item['description']) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: right;">
                                <div style="font-weight: 600; color: var(--text-primary);">
                                    $<?= number_format($item['amount_planned'], 2) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <?php if ($item['status'] === 'active'): ?>
                                    <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span style="background: var(--brand-danger); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        Removed
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <span style="color: var(--text-secondary); font-size: 0.875rem;">
                                    <?= date('M j, Y', strtotime($item['created_at'])) ?>
                                </span>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <div class="d-flex gap-md justify-content-center">
                                    <a
                                        href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/' . $item['id'] . '/edit') ?>"
                                        class="btn btn-primary"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Edit Budget Item"
                                    >
                                        ✏️ Edit
                                    </a>
                                    <button
                                        onclick="showDeleteModal(<?= $item['id'] ?>, '<?= esc($item['item_code']) ?>')"
                                        class="btn btn-danger"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Delete Budget Item"
                                    >
                                        🗑️ Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">💰</div>
            <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Budget Items Yet</h3>
            <p style="margin-bottom: var(--spacing-lg);">Start planning your project budget by adding budget items.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="btn btn-primary">
                💰 Add First Budget Item
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; padding: var(--spacing-xl); border-radius: var(--radius-md); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Delete Budget Item</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete budget item "<span id="deleteItemCode"></span>"?
            This action cannot be undone.
        </p>

        <form id="deleteForm" method="post" style="display: none;">
            <?= csrf_field() ?>
        </form>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Budget Item</button>
        </div>
    </div>
</div>

<script>
let currentDeleteItemId = null;

function showDeleteModal(itemId, itemCode) {
    currentDeleteItemId = itemId;
    document.getElementById('deleteItemCode').textContent = itemCode;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    currentDeleteItemId = null;
}

function confirmDelete() {
    if (currentDeleteItemId) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('admin/projects/' . $project['id'] . '/budgets/') ?>' + currentDeleteItemId + '/delete';
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
