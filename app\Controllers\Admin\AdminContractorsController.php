<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ContractorModel;
use App\Models\ContractorDocumentModel;
use App\Models\ContractorServiceModel;
use App\Models\ContractorComplianceModel;
use App\Models\ContractorAssessmentModel;
use App\Models\ContractorEvaluationModel;
use App\Models\ContractorContactModel;
use App\Models\CountryModel;
use App\Models\ProvinceModel;

/**
 * Admin Contractors Controller
 * 
 * Handles contractor management for the PROMIS Admin Portal including:
 * - Contractor CRUD operations
 * - Document management
 * - Service management
 * - Compliance tracking
 * - Performance assessments
 * - M&E evaluations
 * - Contact management
 */
class AdminContractorsController extends BaseController
{
    protected $contractorModel;
    protected $contractorDocumentModel;
    protected $contractorServiceModel;
    protected $contractorComplianceModel;
    protected $contractorAssessmentModel;
    protected $contractorEvaluationModel;
    protected $contractorContactModel;
    protected $countryModel;
    protected $provinceModel;
    
    public function __construct()
    {
        $this->contractorModel = new ContractorModel();
        $this->contractorDocumentModel = new ContractorDocumentModel();
        $this->contractorServiceModel = new ContractorServiceModel();
        $this->contractorComplianceModel = new ContractorComplianceModel();
        $this->contractorAssessmentModel = new ContractorAssessmentModel();
        $this->contractorEvaluationModel = new ContractorEvaluationModel();
        $this->contractorContactModel = new ContractorContactModel();
        $this->countryModel = new CountryModel();
        $this->provinceModel = new ProvinceModel();
    }

    /**
     * List contractors - GET request
     */
    public function index()
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminOrganizationName = session()->get('admin_organization_name');

        // Get filters from query parameters
        $filters = [
            'status' => $this->request->getGet('status'),
            'business_type' => $this->request->getGet('business_type'),
            'search' => $this->request->getGet('search')
        ];

        // Get contractors with filters
        $contractors = $this->getContractorsWithFilters($adminOrganizationId, $filters);

        // Get contractor statistics
        $stats = $this->contractorModel->getStatistics($adminOrganizationId);

        $data = [
            'title' => 'Contractor Management - PROMIS Admin',
            'page_title' => 'Contractor Management',
            'contractors' => $contractors,
            'stats' => $stats,
            'admin_organization_name' => $adminOrganizationName,
            'filters' => $filters
        ];

        return view('admin/admin_contractors_list', $data);
    }

    /**
     * Show create contractor form - GET request
     */
    public function create()
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        // Get countries and provinces for dropdowns
        $countries = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $provinces = $this->provinceModel->select('provinces.*, countries.name as country_name')
                                        ->join('countries', 'countries.id = provinces.country_id')
                                        ->orderBy('countries.name', 'ASC')
                                        ->orderBy('provinces.name', 'ASC')
                                        ->findAll();

        $data = [
            'title' => 'Create New Contractor - PROMIS Admin',
            'page_title' => 'Create New Contractor',
            'countries' => $countries,
            'provinces' => $provinces
        ];

        return view('admin/admin_contractors_create', $data);
    }

    /**
     * Store new contractor - POST request
     */
    public function store()
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Validate input
        $validationRules = [
            'name' => 'required|max_length[150]',
            'business_type' => 'required|in_list[company,individual,ngo,government]',
            'registration_num' => 'max_length[50]',
            'tax_id' => 'max_length[50]',
            'contact_person' => 'max_length[100]',
            'contact_email' => 'valid_email|max_length[100]',
            'contact_phone' => 'max_length[30]',
            'address_line1' => 'max_length[150]',
            'address_line2' => 'max_length[150]',
            'city' => 'max_length[100]',
            'postal_code' => 'max_length[20]',
            'country_id' => 'integer',
            'province_id' => 'integer'
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        try {
            // Generate contractor code
            $contractorCode = $this->contractorModel->generateContractorCode($adminOrganizationId);

            // Prepare contractor data
            $contractorData = [
                'org_id' => $adminOrganizationId,
                'contractor_code' => $contractorCode,
                'name' => $this->request->getPost('name'),
                'business_type' => $this->request->getPost('business_type'),
                'registration_num' => $this->request->getPost('registration_num'),
                'tax_id' => $this->request->getPost('tax_id'),
                'services_offered' => $this->request->getPost('services_offered'),
                'description' => $this->request->getPost('description'),
                'contact_person' => $this->request->getPost('contact_person'),
                'contact_email' => $this->request->getPost('contact_email'),
                'contact_phone' => $this->request->getPost('contact_phone'),
                'address_line1' => $this->request->getPost('address_line1'),
                'address_line2' => $this->request->getPost('address_line2'),
                'city' => $this->request->getPost('city'),
                'postal_code' => $this->request->getPost('postal_code'),
                'country_id' => $this->request->getPost('country_id') ?: null,
                'province_id' => $this->request->getPost('province_id') ?: null,
                'status' => 'active',
                'created_by' => $adminUserId
            ];

            $contractorId = $this->contractorModel->insert($contractorData);

            if ($contractorId) {
                return redirect()->to(base_url('admin/contractors'))
                               ->with('success', 'Contractor created successfully! Contractor Code: ' . $contractorCode);
            } else {
                return redirect()->back()->withInput()->with('errors', $this->contractorModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating contractor: ' . $e->getMessage());
        }
    }

    /**
     * Show contractor details - GET request
     */
    public function show($id)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get contractor with organization filter
        $contractor = $this->contractorModel->where('id', $id)
                                          ->where('org_id', $adminOrganizationId)
                                          ->where('deleted_at', null)
                                          ->first();

        if (!$contractor) {
            return redirect()->to(base_url('admin/contractors'))
                           ->with('error', 'Contractor not found or access denied.');
        }

        // Get contractor with country information
        $contractor = $this->contractorModel->getWithCountry($id);

        // Get contractor documents
        $documents = $this->contractorDocumentModel->getWithVerificationDetails($id);

        // Get contractor services
        $services = $this->contractorServiceModel->getByContractor($id);

        // Get contractor contacts
        $contacts = $this->contractorContactModel->getByContractor($id);

        // Get compliance status
        $complianceStatus = $this->contractorComplianceModel->getContractorComplianceStatus($id);

        // Get performance summary
        $performanceSummary = $this->contractorAssessmentModel->getPerformanceSummary($id);

        // Get evaluation summary
        $evaluationSummary = $this->contractorEvaluationModel->getEvaluationSummary($id);

        $data = [
            'title' => 'Contractor Profile - PROMIS Admin',
            'page_title' => 'Contractor Profile',
            'contractor' => $contractor,
            'documents' => $documents,
            'services' => $services,
            'contacts' => $contacts,
            'compliance_status' => $complianceStatus,
            'performance_summary' => $performanceSummary,
            'evaluation_summary' => $evaluationSummary
        ];

        return view('admin/admin_contractors_show', $data);
    }

    /**
     * Show edit contractor form - GET request
     */
    public function edit($id)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get contractor with organization filter
        $contractor = $this->contractorModel->where('id', $id)
                                          ->where('org_id', $adminOrganizationId)
                                          ->where('deleted_at', null)
                                          ->first();

        if (!$contractor) {
            return redirect()->to(base_url('admin/contractors'))
                           ->with('error', 'Contractor not found or access denied.');
        }

        // Get countries and provinces for dropdowns
        $countries = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $provinces = $this->provinceModel->select('provinces.*, countries.name as country_name')
                                        ->join('countries', 'countries.id = provinces.country_id')
                                        ->orderBy('countries.name', 'ASC')
                                        ->orderBy('provinces.name', 'ASC')
                                        ->findAll();

        $data = [
            'title' => 'Edit Contractor - PROMIS Admin',
            'page_title' => 'Edit Contractor',
            'contractor' => $contractor,
            'countries' => $countries,
            'provinces' => $provinces
        ];

        return view('admin/admin_contractors_edit', $data);
    }

    /**
     * Update contractor - POST request
     */
    public function update($id)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get contractor with organization filter
        $contractor = $this->contractorModel->where('id', $id)
                                          ->where('org_id', $adminOrganizationId)
                                          ->where('deleted_at', null)
                                          ->first();

        if (!$contractor) {
            return redirect()->to(base_url('admin/contractors'))
                           ->with('error', 'Contractor not found or access denied.');
        }

        // Validate input
        $validationRules = [
            'name' => 'required|max_length[150]',
            'business_type' => 'required|in_list[company,individual,ngo,government]',
            'registration_num' => 'max_length[50]',
            'tax_id' => 'max_length[50]',
            'contact_person' => 'max_length[100]',
            'contact_email' => 'valid_email|max_length[100]',
            'contact_phone' => 'max_length[30]',
            'address_line1' => 'max_length[150]',
            'address_line2' => 'max_length[150]',
            'city' => 'max_length[100]',
            'postal_code' => 'max_length[20]',
            'country_id' => 'integer',
            'province_id' => 'integer',
            'status' => 'in_list[active,suspended,terminated,blacklisted]'
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        try {
            // Prepare update data
            $updateData = [
                'name' => $this->request->getPost('name'),
                'business_type' => $this->request->getPost('business_type'),
                'registration_num' => $this->request->getPost('registration_num'),
                'tax_id' => $this->request->getPost('tax_id'),
                'services_offered' => $this->request->getPost('services_offered'),
                'description' => $this->request->getPost('description'),
                'contact_person' => $this->request->getPost('contact_person'),
                'contact_email' => $this->request->getPost('contact_email'),
                'contact_phone' => $this->request->getPost('contact_phone'),
                'address_line1' => $this->request->getPost('address_line1'),
                'address_line2' => $this->request->getPost('address_line2'),
                'city' => $this->request->getPost('city'),
                'postal_code' => $this->request->getPost('postal_code'),
                'country_id' => $this->request->getPost('country_id') ?: null,
                'province_id' => $this->request->getPost('province_id') ?: null,
                'updated_by' => $adminUserId
            ];

            // Handle status change
            $newStatus = $this->request->getPost('status');
            if ($newStatus && $newStatus !== $contractor['status']) {
                $statusNotes = $this->request->getPost('status_notes');
                $this->contractorModel->updateStatus($id, $newStatus, $statusNotes, $adminUserId);
            } else {
                $updated = $this->contractorModel->update($id, $updateData);
                
                if (!$updated) {
                    return redirect()->back()->withInput()->with('errors', $this->contractorModel->errors());
                }
            }

            return redirect()->to(base_url('admin/contractors'))
                           ->with('success', 'Contractor updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating contractor: ' . $e->getMessage());
        }
    }

    /**
     * Get contractors with filters
     */
    private function getContractorsWithFilters($orgId, $filters)
    {
        $query = $this->contractorModel->select('contractors.*, countries.name as country_name, provinces.name as province_name')
                                     ->join('countries', 'countries.id = contractors.country_id', 'left')
                                     ->join('provinces', 'provinces.id = contractors.province_id', 'left')
                                     ->where('contractors.org_id', $orgId);

        // Apply filters
        if (!empty($filters['status'])) {
            $query = $query->where('contractors.status', $filters['status']);
        }

        if (!empty($filters['business_type'])) {
            $query = $query->where('contractors.business_type', $filters['business_type']);
        }

        if (!empty($filters['search'])) {
            $query = $query->groupStart()
                          ->like('contractors.name', $filters['search'])
                          ->orLike('contractors.contractor_code', $filters['search'])
                          ->orLike('contractors.contact_person', $filters['search'])
                          ->orLike('contractors.contact_email', $filters['search'])
                          ->groupEnd();
        }

        return $query->orderBy('contractors.name', 'ASC')->findAll();
    }
}
