<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/contractors/' . $contractor['id']) ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">👁️</span>
    <span class="btn-text">View Profile</span>
</a>
<a href="<?= base_url('admin/contractors') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to List</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Contractor
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update information for <strong><?= esc($contractor['name']) ?></strong> (<?= esc($contractor['contractor_code']) ?>)
        </p>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header">
        Contractor Information
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <form action="<?= base_url('admin/contractors/' . $contractor['id'] . '/edit') ?>" method="post" class="contractor-edit-form">
            <?= csrf_field() ?>
            
            <!-- Status Management Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 2px solid var(--brand-warning);">
                    Status Management
                </h3>
                
                <div class="form-grid">
                    <!-- Current Status -->
                    <div class="form-group">
                        <label for="status" class="form-label" style="border-left: 3px solid var(--brand-warning); padding-left: var(--spacing-sm);">Status</label>
                        <select 
                            id="status" 
                            name="status" 
                            class="form-input"
                            style="border-color: var(--brand-warning);"
                        >
                            <option value="active" <?= ($contractor['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                            <option value="suspended" <?= ($contractor['status'] === 'suspended') ? 'selected' : '' ?>>Suspended</option>
                            <option value="terminated" <?= ($contractor['status'] === 'terminated') ? 'selected' : '' ?>>Terminated</option>
                            <option value="blacklisted" <?= ($contractor['status'] === 'blacklisted') ? 'selected' : '' ?>>Blacklisted</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current status: <strong><?= ucfirst($contractor['status']) ?></strong>
                        </small>
                    </div>

                    <!-- Status Notes -->
                    <div class="form-group">
                        <label for="status_notes" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Status Notes</label>
                        <textarea 
                            id="status_notes" 
                            name="status_notes" 
                            class="form-input" 
                            rows="3"
                            placeholder="Enter reason for status change (if applicable)"
                            style="border-color: var(--brand-secondary);"
                        ><?= old('status_notes', $contractor['status_notes']) ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Basic Information Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 2px solid var(--brand-primary);">
                    Basic Information
                </h3>
                
                <div class="form-grid">
                    <!-- Contractor Code (Read-only) -->
                    <div class="form-group">
                        <label for="contractor_code" class="form-label" style="border-left: 3px solid var(--text-muted); padding-left: var(--spacing-sm);">Contractor Code</label>
                        <input 
                            type="text" 
                            id="contractor_code" 
                            name="contractor_code" 
                            class="form-input" 
                            value="<?= esc($contractor['contractor_code']) ?>"
                            readonly
                            style="background: var(--bg-tertiary); border-color: var(--text-muted);"
                        >
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Auto-generated code (cannot be changed)
                        </small>
                    </div>

                    <!-- Contractor Name -->
                    <div class="form-group">
                        <label for="name" class="form-label" style="border-left: 3px solid var(--brand-danger); padding-left: var(--spacing-sm);">Contractor Name *</label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            class="form-input" 
                            value="<?= old('name', $contractor['name']) ?>"
                            placeholder="Enter contractor name"
                            required
                            style="border-color: var(--brand-danger);"
                        >
                    </div>

                    <!-- Business Type -->
                    <div class="form-group">
                        <label for="business_type" class="form-label" style="border-left: 3px solid var(--brand-danger); padding-left: var(--spacing-sm);">Business Type *</label>
                        <select 
                            id="business_type" 
                            name="business_type" 
                            class="form-input" 
                            required
                            style="border-color: var(--brand-danger);"
                        >
                            <option value="">Select business type</option>
                            <option value="company" <?= (old('business_type', $contractor['business_type']) === 'company') ? 'selected' : '' ?>>Company</option>
                            <option value="individual" <?= (old('business_type', $contractor['business_type']) === 'individual') ? 'selected' : '' ?>>Individual</option>
                            <option value="ngo" <?= (old('business_type', $contractor['business_type']) === 'ngo') ? 'selected' : '' ?>>NGO</option>
                            <option value="government" <?= (old('business_type', $contractor['business_type']) === 'government') ? 'selected' : '' ?>>Government</option>
                        </select>
                    </div>

                    <!-- Registration Number -->
                    <div class="form-group">
                        <label for="registration_num" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Registration Number</label>
                        <input 
                            type="text" 
                            id="registration_num" 
                            name="registration_num" 
                            class="form-input" 
                            value="<?= old('registration_num', $contractor['registration_num']) ?>"
                            placeholder="Enter registration number"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- Tax ID -->
                    <div class="form-group">
                        <label for="tax_id" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Tax ID</label>
                        <input 
                            type="text" 
                            id="tax_id" 
                            name="tax_id" 
                            class="form-input" 
                            value="<?= old('tax_id', $contractor['tax_id']) ?>"
                            placeholder="Enter tax identification number"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>
                </div>

                <!-- Services Offered -->
                <div class="form-group">
                    <label for="services_offered" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Services Offered</label>
                    <textarea 
                        id="services_offered" 
                        name="services_offered" 
                        class="form-input" 
                        rows="3"
                        placeholder="Describe the services this contractor offers"
                        style="border-color: var(--brand-secondary);"
                    ><?= old('services_offered', $contractor['services_offered']) ?></textarea>
                </div>

                <!-- Description -->
                <div class="form-group">
                    <label for="description" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Description</label>
                    <textarea 
                        id="description" 
                        name="description" 
                        class="form-input" 
                        rows="3"
                        placeholder="Additional information about the contractor"
                        style="border-color: var(--brand-secondary);"
                    ><?= old('description', $contractor['description']) ?></textarea>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 2px solid var(--brand-secondary);">
                    Contact Information
                </h3>
                
                <div class="form-grid">
                    <!-- Contact Person -->
                    <div class="form-group">
                        <label for="contact_person" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Contact Person</label>
                        <input 
                            type="text" 
                            id="contact_person" 
                            name="contact_person" 
                            class="form-input" 
                            value="<?= old('contact_person', $contractor['contact_person']) ?>"
                            placeholder="Enter primary contact person"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- Contact Email -->
                    <div class="form-group">
                        <label for="contact_email" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Contact Email</label>
                        <input 
                            type="email" 
                            id="contact_email" 
                            name="contact_email" 
                            class="form-input" 
                            value="<?= old('contact_email', $contractor['contact_email']) ?>"
                            placeholder="Enter email address"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- Contact Phone -->
                    <div class="form-group">
                        <label for="contact_phone" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Contact Phone</label>
                        <input 
                            type="tel" 
                            id="contact_phone" 
                            name="contact_phone" 
                            class="form-input" 
                            value="<?= old('contact_phone', $contractor['contact_phone']) ?>"
                            placeholder="Enter phone number"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>
                </div>
            </div>

            <!-- Address Information Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 2px solid var(--brand-accent);">
                    Address Information
                </h3>
                
                <div class="form-grid">
                    <!-- Address Line 1 -->
                    <div class="form-group">
                        <label for="address_line1" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Address Line 1</label>
                        <input 
                            type="text" 
                            id="address_line1" 
                            name="address_line1" 
                            class="form-input" 
                            value="<?= old('address_line1', $contractor['address_line1']) ?>"
                            placeholder="Enter street address"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- Address Line 2 -->
                    <div class="form-group">
                        <label for="address_line2" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Address Line 2</label>
                        <input 
                            type="text" 
                            id="address_line2" 
                            name="address_line2" 
                            class="form-input" 
                            value="<?= old('address_line2', $contractor['address_line2']) ?>"
                            placeholder="Enter additional address info"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- City -->
                    <div class="form-group">
                        <label for="city" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">City</label>
                        <input 
                            type="text" 
                            id="city" 
                            name="city" 
                            class="form-input" 
                            value="<?= old('city', $contractor['city']) ?>"
                            placeholder="Enter city"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- State/Province -->
                    <div class="form-group">
                        <label for="state" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">State/Province</label>
                        <input 
                            type="text" 
                            id="state" 
                            name="state" 
                            class="form-input" 
                            value="<?= old('state', $contractor['state']) ?>"
                            placeholder="Enter state or province"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- Postal Code -->
                    <div class="form-group">
                        <label for="postal_code" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Postal Code</label>
                        <input 
                            type="text" 
                            id="postal_code" 
                            name="postal_code" 
                            class="form-input" 
                            value="<?= old('postal_code', $contractor['postal_code']) ?>"
                            placeholder="Enter postal code"
                            style="border-color: var(--brand-secondary);"
                        >
                    </div>

                    <!-- Country -->
                    <div class="form-group">
                        <label for="country_id" class="form-label" style="border-left: 3px solid var(--brand-secondary); padding-left: var(--spacing-sm);">Country</label>
                        <select 
                            id="country_id" 
                            name="country_id" 
                            class="form-input"
                            style="border-color: var(--brand-secondary);"
                        >
                            <option value="">Select country</option>
                            <?php foreach ($countries as $country): ?>
                                <option value="<?= $country['id'] ?>" <?= (old('country_id', $contractor['country_id']) == $country['id']) ? 'selected' : '' ?>>
                                    <?= esc($country['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/contractors/' . $contractor['id']) ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">❌</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">✅</span>
                    <span class="btn-text">Update Contractor</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Mobile-friendly CSS -->
<style>
/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #E5E7EB;
    flex-wrap: wrap;
}

/* Responsive grid for forms */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

/* Enhanced form inputs */
.form-input {
    min-height: 44px;
    font-size: 0.875rem;
    padding: var(--spacing-md);
}

/* Mobile enhancements */
@media (max-width: 768px) {
    .btn-mobile {
        min-height: 48px;
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .form-actions {
        justify-content: stretch;
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }
    
    .form-input {
        min-height: 48px;
        font-size: 16px; /* Prevents iOS zoom */
    }
}
</style>

<?= $this->endSection() ?>
