<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Risks</span>
</a>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-risks" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">👁️</span>
    <span class="btn-text">View Project</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create New Risk
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Add a new risk for: <strong><?= esc($project['title']) ?></strong> (<?= esc($project['pro_code']) ?>)
        </p>
    </div>
</div>

<!-- Create Risk Form -->
<div class="card">
    <div class="card-header">
        ⚠️ Risk Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/risks/create') ?>" class="risk-create-form">
            <?= csrf_field() ?>

            <!-- Risk Description -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="description" class="form-label">
                    Risk Description <span style="color: var(--brand-danger);">*</span>
                </label>
                <textarea id="description"
                          name="description"
                          class="form-input"
                          style="min-height: 120px; resize: vertical;"
                          placeholder="Describe the potential risk in detail..."
                          required><?= old('description') ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Clear description of what could go wrong and its potential impact
                </small>
                <?php if (isset($errors['description'])): ?>
                    <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                        <?= esc($errors['description']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column - Risk Classification -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        📋 Risk Classification
                    </h3>

                    <!-- Risk Type -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="risk_type" class="form-label">
                            Risk Type <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="risk_type"
                                name="risk_type"
                                class="form-input"
                                required>
                            <option value="">Select Risk Type</option>
                            <option value="proposed" <?= old('risk_type') === 'proposed' ? 'selected' : '' ?>>Proposed - Identified during planning</option>
                            <option value="foreseen" <?= old('risk_type') === 'foreseen' ? 'selected' : '' ?>>Foreseen - Anticipated during implementation</option>
                            <option value="witnessed" <?= old('risk_type') === 'witnessed' ? 'selected' : '' ?>>Witnessed - Actually occurred</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            When was this risk identified in the project lifecycle?
                        </small>
                        <?php if (isset($errors['risk_type'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['risk_type']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Risk Level -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="risk_level" class="form-label">
                            Risk Level <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="risk_level"
                                name="risk_level"
                                class="form-input"
                                required>
                            <option value="">Select Risk Level</option>
                            <option value="low" <?= old('risk_level') === 'low' ? 'selected' : '' ?>>Low - Minor impact</option>
                            <option value="medium" <?= old('risk_level') === 'medium' ? 'selected' : '' ?>>Medium - Moderate impact</option>
                            <option value="high" <?= old('risk_level') === 'high' ? 'selected' : '' ?>>High - Significant impact</option>
                            <option value="critical" <?= old('risk_level') === 'critical' ? 'selected' : '' ?>>Critical - Severe impact</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            How severely would this risk affect the project?
                        </small>
                        <?php if (isset($errors['risk_level'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['risk_level']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column - Management Information -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        🎯 Management Information
                    </h3>

                    <!-- Milestone Association -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="milestone_id" class="form-label">
                            Associated Milestone
                        </label>
                        <select id="milestone_id"
                                name="milestone_id"
                                class="form-input">
                            <option value="">No specific milestone</option>
                            <?php foreach ($milestones as $milestone): ?>
                                <option value="<?= $milestone['id'] ?>" <?= old('milestone_id') == $milestone['id'] ? 'selected' : '' ?>>
                                    <?= esc($milestone['milestone_code']) ?> - <?= esc($milestone['title']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Link this risk to a specific project milestone (optional)
                        </small>
                        <?php if (isset($errors['milestone_id'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['milestone_id']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Approval Status -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="approval_status" class="form-label">
                            Approval Status <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="approval_status"
                                name="approval_status"
                                class="form-input"
                                required>
                            <option value="">Select Status</option>
                            <option value="pending" <?= old('approval_status') === 'pending' ? 'selected' : '' ?>>Pending - Awaiting review</option>
                            <option value="approved" <?= old('approval_status') === 'approved' ? 'selected' : '' ?>>Approved - Confirmed risk</option>
                            <option value="rejected" <?= old('approval_status') === 'rejected' ? 'selected' : '' ?>>Rejected - Not a valid risk</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current approval status of this risk
                        </small>
                        <?php if (isset($errors['approval_status'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['approval_status']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Mitigation Strategy -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="mitigation" class="form-label">
                    Mitigation Strategy
                </label>
                <textarea id="mitigation"
                          name="mitigation"
                          class="form-input"
                          style="min-height: 100px; resize: vertical;"
                          placeholder="Describe how this risk can be prevented, reduced, or managed..."><?= old('mitigation') ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Optional. Describe preventive measures or response plans
                </small>
                <?php if (isset($errors['mitigation'])): ?>
                    <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                        <?= esc($errors['mitigation']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Risk Level Preview -->
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📊 Risk Level Preview
                </h4>
                <div id="riskLevelPreview" style="font-size: 1.1rem; color: var(--text-secondary);">
                    Select risk level to see severity indicator
                </div>
                <small style="color: var(--text-muted); margin-top: var(--spacing-sm); display: block;">
                    Risk levels help prioritize attention and resources
                </small>
            </div>

            <!-- Examples Section -->
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    💡 Examples of Project Risks
                </h4>
                <div style="font-size: 0.75rem; color: var(--text-secondary); line-height: 1.5;">
                    <div><strong>Proposed:</strong> "Budget constraints may delay procurement" (Risk Level: Medium)</div>
                    <div><strong>Foreseen:</strong> "Weather conditions may affect construction schedule" (Risk Level: High)</div>
                    <div><strong>Witnessed:</strong> "Key stakeholder withdrew support" (Risk Level: Critical)</div>
                    <div><strong>Technical:</strong> "Equipment failure may disrupt operations" (Risk Level: High)</div>
                    <div><strong>Financial:</strong> "Currency fluctuation may increase costs" (Risk Level: Medium)</div>
                </div>
            </div>

            <!-- Important Note -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--brand-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    ℹ️ Important Note
                </h4>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem; line-height: 1.5;">
                    <strong>Risk Management:</strong> Document risks throughout the project lifecycle for proactive management.<br>
                    <strong>Approval Process:</strong> Risks require approval before being included in formal risk registers.
                </p>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">⚠️</span>
                    <span class="btn-text">Create Risk</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Risk level preview
function updateRiskLevelPreview() {
    const riskLevel = document.getElementById('risk_level').value;
    const preview = document.getElementById('riskLevelPreview');

    if (riskLevel) {
        let levelClass = 'var(--text-secondary)';
        let levelLabel = '';

        switch(riskLevel) {
            case 'low':
                levelClass = 'var(--brand-success)';
                levelLabel = 'Low Risk - Minor impact, manageable';
                break;
            case 'medium':
                levelClass = 'var(--brand-warning)';
                levelLabel = 'Medium Risk - Moderate impact, requires attention';
                break;
            case 'high':
                levelClass = 'var(--brand-danger)';
                levelLabel = 'High Risk - Significant impact, needs immediate action';
                break;
            case 'critical':
                levelClass = 'var(--brand-danger)';
                levelLabel = 'Critical Risk - Severe impact, urgent intervention required';
                break;
        }

        preview.innerHTML = `<span style="color: ${levelClass}; font-weight: 600;">${levelLabel}</span>`;
    } else {
        preview.textContent = 'Select risk level to see severity indicator';
    }
}

// Add event listener
document.getElementById('risk_level').addEventListener('change', updateRiskLevelPreview);

// Initial calculation if values are pre-filled
document.addEventListener('DOMContentLoaded', updateRiskLevelPreview);
</script>

<style>
/* 
Color-coded Form Input System:
- RED OUTLINE: Required fields (must be filled)
- GREEN OUTLINE: Optional fields (can be left empty)
*/

/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

/* Form input styling - applies to all input types */
.form-input,
input.form-input,
textarea.form-input,
select.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 44px;
    border: 2px solid #10B981 !important; /* Default green for optional fields */
}

/* Required fields have red outline - applies to all input types */
.form-input[required],
input.form-input[required],
textarea.form-input[required],
select.form-input[required] {
    border: 2px solid #EF4444 !important; /* Red for required fields */
}

/* Focus states for optional fields (green) */
.form-input:focus {
    outline: none;
    border-width: 2px;
    border-color: #059669 !important; /* Darker green on focus */
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Focus states for required fields (red) */
.form-input[required]:focus,
input.form-input[required]:focus,
textarea.form-input[required]:focus,
select.form-input[required]:focus {
    outline: none;
    border-width: 2px;
    border-color: #DC2626 !important; /* Darker red on focus */
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Hover states for optional fields (green) */
.form-input:hover {
    border-color: #059669 !important; /* Slightly darker green on hover */
}

/* Hover states for required fields (red) */
.form-input[required]:hover,
input.form-input[required]:hover,
textarea.form-input[required]:hover,
select.form-input[required]:hover {
    border-color: #DC2626 !important; /* Slightly darker red on hover */
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}



/* Enhanced select dropdowns */
select.form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 120px;
}

/* Enhanced number inputs */
input[type="number"].form-input {
    text-align: right;
}

/* Form group styling */
.form-group {
    margin-bottom: var(--spacing-lg);
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    .form-actions {
        gap: var(--spacing-sm);
    }

    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Loading state for submit button */
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}
</style>

<?= $this->endSection() ?>
