<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
    ← Back to Documents
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            📁 Upload Project Document
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Add a new document to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Upload Form -->
<div style="max-width: 800px; margin: 0 auto;">
    <div class="card">
        <div class="card-header">
            📁 Document Upload Form
        </div>
        <div style="padding: var(--spacing-xl);">
            <?php if (session()->getFlashdata('errors')): ?>
                <div style="background: var(--bg-danger); color: var(--text-danger); padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                    <ul style="margin: 0; padding-left: var(--spacing-lg);">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div style="background: var(--bg-danger); color: var(--text-danger); padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                    <?= esc(session()->getFlashdata('error')) ?>
                </div>
            <?php endif; ?>
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= esc(session()->getFlashdata('error')) ?>
                        </div>
                    <?php endif; ?>

            <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" enctype="multipart/form-data">
                <?= csrf_field() ?>

                <div style="display: grid; gap: var(--spacing-lg);">
                    <!-- Document Category -->
                    <div>
                        <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">
                            Document Category
                        </label>
                        <select name="category" id="category"
                                class="form-input">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $key => $label): ?>
                                <option value="<?= $key ?>" <?= (old('category') == $key) ? 'selected' : '' ?>>
                                    <?= esc($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                            Choose the appropriate document category (optional)
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="3" maxlength="255"
                                  class="form-input" style="resize: vertical;"><?= old('description') ?></textarea>
                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                            Optional description or notes about the document (max 255 characters)
                        </div>
                    </div>

                    <!-- Document File -->
                    <div>
                        <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">
                            Document File <span style="color: var(--text-danger);">*</span>
                        </label>
                        <input type="file" id="document_file" name="document_file" required
                               class="form-input">
                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                            <strong>File Requirements:</strong>
                            <ul style="margin: var(--spacing-xs) 0 0 var(--spacing-lg); padding: 0;">
                                <li>Maximum file size: 25MB</li>
                                <li>Allowed file types: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF, ODT, ODS, ODP, JPG, JPEG, PNG, GIF, BMP, TIFF, SVG, ZIP, RAR, 7Z, TAR, GZ, CSV, XML, JSON</li>
                                <li>Executable files (.exe, .bat) and application files are not permitted</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div style="background: var(--bg-info); color: var(--text-info); padding: var(--spacing-md); border-radius: var(--radius-md); border-left: 4px solid var(--brand-info);">
                        <div style="display: flex; align-items: flex-start; gap: var(--spacing-sm);">
                            <div style="font-size: 1.2rem;">ℹ️</div>
                            <div>
                                <strong>Important:</strong> Once uploaded, the document will be stored securely and can be downloaded by authorized users.
                                You can update the document details or replace the file later if needed.
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div style="display: flex; justify-content: space-between; gap: var(--spacing-md);">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
                            ❌ Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            📁 Upload Document
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// File size validation
document.getElementById('document_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const maxSize = 25 * 1024 * 1024; // 25MB in bytes
        if (file.size > maxSize) {
            alert('File size exceeds 25MB limit. Please choose a smaller file.');
            e.target.value = '';
            return;
        }
        
        // Display file info
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.remove();
        }
        
        const info = document.createElement('div');
        info.id = 'fileInfo';
        info.className = 'mt-2 p-2 bg-light rounded';
        info.innerHTML = `
            <small>
                <strong>Selected file:</strong> ${file.name}<br>
                <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                <strong>Type:</strong> ${file.type || 'Unknown'}
            </small>
        `;
        e.target.parentNode.appendChild(info);
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const file = document.getElementById('document_file').files[0];

    if (!file) {
        alert('Please select a file to upload.');
        e.preventDefault();
        return;
    }

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
});
</script>

<style>
/* Color-coded Form Input System */
.form-input,
select.form-input,
input.form-input,
textarea.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.95rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid #10B981 !important; /* Green for optional */
    transition: border-color 0.2s;
}
.form-input[required],
select.form-input[required],
input.form-input[required],
textarea.form-input[required] {
    border: 2px solid #EF4444 !important; /* Red for required */
}
.form-input:focus {
    border-color: #059669 !important;
    box-shadow: 0 0 0 3px rgba(16,185,129,0.1);
}
.form-input[required]:focus {
    border-color: #DC2626 !important;
    box-shadow: 0 0 0 3px rgba(239,68,68,0.1);
}
.form-input:hover {
    border-color: #059669 !important;
}
.form-input[required]:hover {
    border-color: #DC2626 !important;
}
input[type="file"].form-input {
    cursor: pointer;
}
input[type="file"].form-input::-webkit-file-upload-button {
    background: var(--brand-primary);
    color: white;
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    margin-right: var(--spacing-sm);
    cursor: pointer;
}
@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-md) !important;
        font-size: 0.875rem !important;
    }
    .card {
        margin: var(--spacing-sm);
    }
    div[style*="padding: var(--spacing-xl)"] {
        padding: var(--spacing-lg) !important;
    }
}
</style>
<?= $this->endSection() ?>
