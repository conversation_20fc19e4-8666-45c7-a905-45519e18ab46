<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/contractors/' . $contractor['id'] . '/edit') ?>" class="btn btn-primary btn-mobile">
    <span class="btn-icon">✏️</span>
    <span class="btn-text">Edit Contractor</span>
</a>
<a href="<?= base_url('admin/contractors') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to List</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            <?= esc($contractor['name']) ?>
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Contractor Code: <strong><?= esc($contractor['contractor_code']) ?></strong>
            <?php
            $statusColors = [
                'active' => 'var(--brand-secondary)',
                'suspended' => 'var(--brand-warning)',
                'terminated' => 'var(--brand-danger)',
                'blacklisted' => '#DC2626'
            ];
            $statusColor = $statusColors[$contractor['status']] ?? 'var(--text-muted)';
            ?>
            <span style="display: inline-block; margin-left: var(--spacing-md); padding: var(--spacing-xs) var(--spacing-sm); background: <?= $statusColor ?>; color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                <?= esc($contractor['status']) ?>
            </span>
        </p>
    </div>
</div>

<!-- Overview Cards -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
    <!-- Basic Info Card -->
    <div class="card" style="padding: var(--spacing-lg);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-lg); background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-weight: 700; color: white; font-size: 1.25rem;">
                <?= strtoupper(substr($contractor['name'], 0, 2)) ?>
            </div>
            <div>
                <div style="font-weight: 600; color: var(--text-primary);">Basic Information</div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Company details</div>
            </div>
        </div>
        <div style="space-y: var(--spacing-sm);">
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Business Type:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm); text-transform: capitalize;"><?= esc($contractor['business_type']) ?></span>
            </div>
            <?php if ($contractor['registration_num']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Registration:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= esc($contractor['registration_num']) ?></span>
            </div>
            <?php endif; ?>
            <?php if ($contractor['tax_id']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Tax ID:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= esc($contractor['tax_id']) ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Contact Info Card -->
    <div class="card" style="padding: var(--spacing-lg);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-lg); background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                📞
            </div>
            <div>
                <div style="font-weight: 600; color: var(--text-primary);">Contact Information</div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Primary contact details</div>
            </div>
        </div>
        <div style="space-y: var(--spacing-sm);">
            <?php if ($contractor['contact_person']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Contact Person:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= esc($contractor['contact_person']) ?></span>
            </div>
            <?php endif; ?>
            <?php if ($contractor['contact_email']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Email:</span>
                <a href="mailto:<?= esc($contractor['contact_email']) ?>" style="font-weight: 600; margin-left: var(--spacing-sm); color: var(--brand-primary);"><?= esc($contractor['contact_email']) ?></a>
            </div>
            <?php endif; ?>
            <?php if ($contractor['contact_phone']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Phone:</span>
                <a href="tel:<?= esc($contractor['contact_phone']) ?>" style="font-weight: 600; margin-left: var(--spacing-sm); color: var(--brand-primary);"><?= esc($contractor['contact_phone']) ?></a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Performance Summary Card -->
    <div class="card" style="padding: var(--spacing-lg);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-lg); background: var(--gradient-accent); display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                📊
            </div>
            <div>
                <div style="font-weight: 600; color: var(--text-primary);">Performance Summary</div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Assessment overview</div>
            </div>
        </div>
        <div style="space-y: var(--spacing-sm);">
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Total Assessments:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= $performance_summary['total_assessments'] ?></span>
            </div>
            <?php if ($performance_summary['average_scores']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Overall Score:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm); color: var(--brand-primary);"><?= $performance_summary['average_scores']['overall'] ?>/5.0</span>
            </div>
            <?php endif; ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Compliance Status:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm); text-transform: capitalize;"><?= $compliance_status['overall_status'] ?></span>
            </div>
        </div>
    </div>
</div>

<!-- Address Information -->
<?php if ($contractor['address_line1'] || $contractor['city'] || $contractor['province_name'] || $contractor['country_name']): ?>
<div class="card mb-xl">
    <div class="card-header">
        📍 Address Information
    </div>
    <div style="padding: var(--spacing-lg);">
        <div style="color: var(--text-primary); line-height: 1.6;">
            <?php if ($contractor['address_line1']): ?>
                <?= esc($contractor['address_line1']) ?><br>
            <?php endif; ?>
            <?php if ($contractor['address_line2']): ?>
                <?= esc($contractor['address_line2']) ?><br>
            <?php endif; ?>
            <?php if ($contractor['city'] || $contractor['state'] || $contractor['postal_code']): ?>
                <?= esc($contractor['city']) ?>
                <?php if ($contractor['state']): ?>, <?= esc($contractor['state']) ?><?php endif; ?>
                <?php if ($contractor['postal_code']): ?> <?= esc($contractor['postal_code']) ?><?php endif; ?><br>
            <?php endif; ?>
            <?php if ($contractor['province_name']): ?>
                <?= esc($contractor['province_name']) ?><br>
            <?php endif; ?>
            <?php if ($contractor['country_name']): ?>
                <?= esc($contractor['country_name']) ?>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Services Offered -->
<?php if ($contractor['services_offered'] || !empty($services)): ?>
<div class="card mb-xl">
    <div class="card-header">
        🔧 Services & Capabilities
    </div>
    <div style="padding: var(--spacing-lg);">
        <?php if ($contractor['services_offered']): ?>
            <div style="margin-bottom: var(--spacing-lg);">
                <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin-bottom: var(--spacing-sm);">Services Overview</h4>
                <p style="color: var(--text-secondary); line-height: 1.6; margin: 0;"><?= esc($contractor['services_offered']) ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($services)): ?>
            <div>
                <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin-bottom: var(--spacing-md);">Detailed Services</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-md);">
                    <?php foreach ($services as $service): ?>
                        <div style="padding: var(--spacing-md); background: var(--bg-tertiary); border-radius: var(--radius-md); border-left: 4px solid <?= $service['is_primary'] ? 'var(--brand-primary)' : 'var(--brand-secondary)' ?>;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                                <span style="font-weight: 600; color: var(--text-primary);"><?= esc($service['service_name']) ?></span>
                                <?php if ($service['is_primary']): ?>
                                    <span style="padding: var(--spacing-xs); background: var(--brand-primary); color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">PRIMARY</span>
                                <?php endif; ?>
                            </div>
                            <div style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                                Category: <?= esc($service['service_category']) ?>
                            </div>
                            <?php if ($service['description']): ?>
                                <div style="color: var(--text-secondary); font-size: 0.875rem;">
                                    <?= esc($service['description']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

<!-- Description -->
<?php if ($contractor['description']): ?>
<div class="card mb-xl">
    <div class="card-header">
        📝 Description
    </div>
    <div style="padding: var(--spacing-lg);">
        <p style="color: var(--text-secondary); line-height: 1.6; margin: 0;"><?= esc($contractor['description']) ?></p>
    </div>
</div>
<?php endif; ?>

<!-- Documents Section -->
<div class="card mb-xl">
    <div class="card-header">
        📄 Documents (<?= count($documents) ?>)
    </div>
    <?php if (!empty($documents)): ?>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Document</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Expiry</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($documents as $document): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600; color: var(--text-primary);"><?= esc($document['doc_title']) ?></div>
                                <?php if ($document['doc_number']): ?>
                                    <div style="color: var(--text-muted); font-size: 0.875rem;"><?= esc($document['doc_number']) ?></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span style="display: inline-block; padding: var(--spacing-xs) var(--spacing-sm); background: var(--bg-tertiary); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($document['doc_type']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($document['is_verified']): ?>
                                    <span style="color: var(--brand-secondary); font-weight: 600;">✅ Verified</span>
                                    <?php if ($document['verified_by_name']): ?>
                                        <div style="color: var(--text-muted); font-size: 0.75rem;">by <?= esc($document['verified_by_name']) ?></div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--brand-warning); font-weight: 600;">⏳ Pending</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($document['expiry_date']): ?>
                                    <?php
                                    $expiryDate = new DateTime($document['expiry_date']);
                                    $today = new DateTime();
                                    $isExpired = $expiryDate < $today;
                                    $isExpiringSoon = $expiryDate->diff($today)->days <= 30 && !$isExpired;
                                    ?>
                                    <div style="color: <?= $isExpired ? 'var(--brand-danger)' : ($isExpiringSoon ? 'var(--brand-warning)' : 'var(--text-secondary)') ?>;">
                                        <?= $expiryDate->format('M d, Y') ?>
                                        <?php if ($isExpired): ?>
                                            <div style="font-size: 0.75rem; font-weight: 600;">EXPIRED</div>
                                        <?php elseif ($isExpiringSoon): ?>
                                            <div style="font-size: 0.75rem; font-weight: 600;">EXPIRING SOON</div>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span style="color: var(--text-muted);">No expiry</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?= base_url($document['doc_path']) ?>" target="_blank" class="btn btn-secondary btn-mobile" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    <span class="btn-icon">📄</span>
                                    <span class="btn-text">View</span>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="padding: var(--spacing-xl); text-align: center;">
            <div style="font-size: 2rem; margin-bottom: var(--spacing-md); opacity: 0.5;">📄</div>
            <p style="color: var(--text-muted);">No documents uploaded yet.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Contacts Section -->
<div class="card mb-xl">
    <div class="card-header">
        👥 Contacts (<?= count($contacts) ?>)
    </div>
    <?php if (!empty($contacts)): ?>
        <div style="padding: var(--spacing-lg);">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-lg);">
                <?php foreach ($contacts as $contact): ?>
                    <div style="padding: var(--spacing-md); background: var(--bg-tertiary); border-radius: var(--radius-md); border-left: 4px solid <?= $contact['is_primary'] ? 'var(--brand-primary)' : 'var(--brand-secondary)' ?>;">
                        <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                            <span style="font-weight: 600; color: var(--text-primary);"><?= esc($contact['name']) ?></span>
                            <?php if ($contact['is_primary']): ?>
                                <span style="padding: var(--spacing-xs); background: var(--brand-primary); color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">PRIMARY</span>
                            <?php endif; ?>
                        </div>
                        <?php if ($contact['position']): ?>
                            <div style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                                <?= esc($contact['position']) ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($contact['email']): ?>
                            <div style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                                📧 <a href="mailto:<?= esc($contact['email']) ?>" style="color: var(--brand-primary);"><?= esc($contact['email']) ?></a>
                            </div>
                        <?php endif; ?>
                        <?php if ($contact['phone']): ?>
                            <div style="color: var(--text-secondary); font-size: 0.875rem);">
                                📞 <a href="tel:<?= esc($contact['phone']) ?>" style="color: var(--brand-primary);"><?= esc($contact['phone']) ?></a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php else: ?>
        <div style="padding: var(--spacing-xl); text-align: center;">
            <div style="font-size: 2rem; margin-bottom: var(--spacing-md); opacity: 0.5;">👥</div>
            <p style="color: var(--text-muted);">No contacts added yet.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Mobile-friendly CSS -->
<style>
/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Mobile enhancements */
@media (max-width: 768px) {
    .btn-mobile {
        min-height: 48px;
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .table-container {
        overflow-x: auto;
    }
}
</style>

<?= $this->endSection() ?>
