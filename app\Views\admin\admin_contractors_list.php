<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/contractors/create') ?>" class="btn btn-primary btn-mobile">
    <span class="btn-icon">🏗️</span>
    <span class="btn-text">Create New Contractor</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Contractor Management
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage contractors for <strong><?= esc($admin_organization_name) ?></strong>
        </p>
    </div>
</div>

<!-- Statistics Cards -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📊</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
            <?= $stats['total'] ?? 0 ?>
        </div>
        <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Contractors</div>
    </div>
    
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">✅</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            <?= $stats['by_status']['active'] ?? 0 ?>
        </div>
        <div style="color: var(--text-secondary); font-size: 0.875rem;">Active</div>
    </div>
    
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">⚠️</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-warning); margin-bottom: var(--spacing-xs);">
            <?= ($stats['by_status']['suspended'] ?? 0) + ($stats['by_status']['terminated'] ?? 0) ?>
        </div>
        <div style="color: var(--text-secondary); font-size: 0.875rem;">Inactive</div>
    </div>
    
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">🏢</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-accent); margin-bottom: var(--spacing-xs);">
            <?= $stats['by_type']['company'] ?? 0 ?>
        </div>
        <div style="color: var(--text-secondary); font-size: 0.875rem;">Companies</div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-xl">
    <div class="card-header">
        Filters & Search
    </div>
    <div style="padding: var(--spacing-lg);">
        <form method="get" action="<?= base_url('admin/contractors') ?>" class="form-grid" id="contractorFilterForm">

            <!-- Search -->
            <div class="form-group">
                <label for="search" class="form-label">Search</label>
                <input
                    type="text"
                    id="search"
                    name="search"
                    class="form-input"
                    placeholder="Search contractors..."
                    value="<?= esc($filters['search']) ?>"
                >
            </div>

            <!-- Status Filter -->
            <div class="form-group">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-input">
                    <option value="">All Status</option>
                    <option value="active" <?= ($filters['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                    <option value="suspended" <?= ($filters['status'] === 'suspended') ? 'selected' : '' ?>>Suspended</option>
                    <option value="terminated" <?= ($filters['status'] === 'terminated') ? 'selected' : '' ?>>Terminated</option>
                    <option value="blacklisted" <?= ($filters['status'] === 'blacklisted') ? 'selected' : '' ?>>Blacklisted</option>
                </select>
            </div>

            <!-- Business Type Filter -->
            <div class="form-group">
                <label for="business_type" class="form-label">Business Type</label>
                <select name="business_type" id="business_type" class="form-input">
                    <option value="">All Types</option>
                    <option value="company" <?= ($filters['business_type'] === 'company') ? 'selected' : '' ?>>Company</option>
                    <option value="individual" <?= ($filters['business_type'] === 'individual') ? 'selected' : '' ?>>Individual</option>
                    <option value="ngo" <?= ($filters['business_type'] === 'ngo') ? 'selected' : '' ?>>NGO</option>
                    <option value="government" <?= ($filters['business_type'] === 'government') ? 'selected' : '' ?>>Government</option>
                </select>
            </div>

            <!-- Organization Context -->
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div style="display: flex; align-items: center; gap: var(--spacing-sm); background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-sm) var(--spacing-md);">
                    <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-size: 0.875rem;">
                        🏢
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">
                            <?= esc($admin_organization_name) ?>
                        </div>
                        <div style="color: var(--text-muted); font-size: 0.75rem;">
                            Your Organization
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Filter Actions -->
        <div class="form-actions">
            <a href="<?= base_url('admin/contractors') ?>" class="btn btn-secondary btn-mobile">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">Clear</span>
            </a>
            <button type="submit" form="contractorFilterForm" class="btn btn-primary btn-mobile">
                <span class="btn-icon">🔍</span>
                <span class="btn-text">Filter</span>
            </button>
        </div>
    </div>
</div>

<!-- Contractors Table -->
<div class="card">
    <div class="card-header">
        Contractors (<?= count($contractors) ?> found)
    </div>
    
    <?php if (!empty($contractors)): ?>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Contractor</th>
                        <th>Business Type</th>
                        <th>Contact</th>
                        <th>Location</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($contractors as $contractor): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                    <div style="width: 40px; height: 40px; border-radius: var(--radius-md); background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-weight: 600; color: white; font-size: 0.875rem;">
                                        <?= strtoupper(substr($contractor['name'], 0, 2)) ?>
                                    </div>
                                    <div>
                                        <div style="font-weight: 600; color: var(--text-primary);">
                                            <?= esc($contractor['name']) ?>
                                        </div>
                                        <div style="color: var(--text-muted); font-size: 0.875rem;">
                                            <?= esc($contractor['contractor_code']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span style="display: inline-block; padding: var(--spacing-xs) var(--spacing-sm); background: var(--bg-tertiary); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($contractor['business_type']) ?>
                                </span>
                            </td>
                            <td>
                                <div>
                                    <?php if ($contractor['contact_person']): ?>
                                        <div style="font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">
                                            <?= esc($contractor['contact_person']) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($contractor['contact_email']): ?>
                                        <div style="color: var(--text-secondary); font-size: 0.75rem;">
                                            <?= esc($contractor['contact_email']) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($contractor['contact_phone']): ?>
                                        <div style="color: var(--text-secondary); font-size: 0.75rem;">
                                            <?= esc($contractor['contact_phone']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem;">
                                    <?php if ($contractor['city']): ?>
                                        <div style="color: var(--text-primary);"><?= esc($contractor['city']) ?></div>
                                    <?php endif; ?>
                                    <?php if ($contractor['province_name']): ?>
                                        <div style="color: var(--text-secondary); font-size: 0.75rem;"><?= esc($contractor['province_name']) ?></div>
                                    <?php endif; ?>
                                    <?php if ($contractor['country_name']): ?>
                                        <div style="color: var(--text-secondary); font-size: 0.75rem;"><?= esc($contractor['country_name']) ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php
                                $statusColors = [
                                    'active' => 'var(--brand-secondary)',
                                    'suspended' => 'var(--brand-warning)',
                                    'terminated' => 'var(--brand-danger)',
                                    'blacklisted' => '#DC2626'
                                ];
                                $statusColor = $statusColors[$contractor['status']] ?? 'var(--text-muted)';
                                ?>
                                <span style="display: inline-block; padding: var(--spacing-xs) var(--spacing-sm); background: <?= $statusColor ?>; color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($contractor['status']) ?>
                                </span>
                            </td>
                            <td>
                                <div style="display: flex; gap: var(--spacing-xs);">
                                    <a href="<?= base_url('admin/contractors/' . $contractor['id']) ?>" 
                                       class="btn btn-secondary btn-mobile" 
                                       style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        <span class="btn-icon">👁️</span>
                                        <span class="btn-text">View</span>
                                    </a>
                                    <a href="<?= base_url('admin/contractors/' . $contractor['id'] . '/edit') ?>" 
                                       class="btn btn-primary btn-mobile" 
                                       style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        <span class="btn-icon">✏️</span>
                                        <span class="btn-text">Edit</span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="padding: var(--spacing-xl); text-align: center;">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md); opacity: 0.5;">🏗️</div>
            <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No contractors found</h3>
            <p style="color: var(--text-muted); margin-bottom: var(--spacing-lg);">
                <?php if (!empty($filters['search']) || !empty($filters['status']) || !empty($filters['business_type'])): ?>
                    No contractors match your current filters. Try adjusting your search criteria.
                <?php else: ?>
                    You haven't added any contractors yet. Create your first contractor to get started.
                <?php endif; ?>
            </p>
            <a href="<?= base_url('admin/contractors/create') ?>" class="btn btn-primary btn-mobile">
                <span class="btn-icon">🏗️</span>
                <span class="btn-text">Create First Contractor</span>
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Mobile-friendly CSS -->
<style>
/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #E5E7EB;
    flex-wrap: wrap;
}

/* Responsive grid for forms */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

/* Mobile enhancements */
@media (max-width: 768px) {
    .btn-mobile {
        min-height: 48px;
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .form-actions {
        justify-content: stretch;
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }
    
    .table-container {
        overflow-x: auto;
    }
}
</style>

<?= $this->endSection() ?>
