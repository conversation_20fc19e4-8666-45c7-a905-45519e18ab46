<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-outcomes" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/create') ?>" class="btn btn-primary">
    🎯 Add Outcome
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Outcomes
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Measurable deliverables for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Outcome Statistics -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
    <!-- Total Outcomes -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">🎯</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
            <?= $outcomeStats['total_outcomes'] ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Total Outcomes</div>
    </div>

    <!-- Total Quantity -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📊</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            <?= number_format($outcomeStats['total_quantity'], 2) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Total Quantity</div>
    </div>

    <!-- Average Quantity -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📈</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-accent); margin-bottom: var(--spacing-xs);">
            <?= number_format($outcomeStats['average_quantity'], 2) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Average Quantity</div>
    </div>

    <!-- Units Count -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📏</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-warning); margin-bottom: var(--spacing-xs);">
            <?= count($outcomeStats['by_unit']) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Different Units</div>
    </div>
</div>

<!-- Outcomes by Unit -->
<?php if (!empty($outcomeStats['by_unit'])): ?>
<div class="card mb-xl">
    <div class="card-header">
        📏 Outcomes by Unit
    </div>
    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg);">
            <?php foreach ($outcomeStats['by_unit'] as $unitStat): ?>
                <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); text-align: center;">
                    <div style="font-weight: 700; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                        <?= esc($unitStat['unit'] ?: 'No Unit') ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">
                        <?= $unitStat['count'] ?> outcomes • <?= number_format($unitStat['total_quantity'], 2) ?> total
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Outcomes Table -->
<div class="card">
    <div class="card-header">
        🎯 Project Outcomes
    </div>

    <?php if (!empty($outcomes)): ?>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: var(--bg-tertiary); border-bottom: 1px solid var(--border-color);">
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            Outcome Description
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Quantity
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Unit
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Created
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($outcomes as $outcome): ?>
                        <tr style="border-bottom: 1px solid var(--border-color);">
                            <td style="padding: var(--spacing-md);">
                                <div style="color: var(--text-primary); font-weight: 600;">
                                    <?= esc($outcome['outcome_text']) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <div style="font-weight: 600; color: var(--brand-primary);">
                                    <?= number_format($outcome['quantity'], 2) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <?php if ($outcome['unit']): ?>
                                    <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        <?= esc($outcome['unit']) ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-style: italic;">No unit</span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <span style="color: var(--text-secondary); font-size: 0.875rem;">
                                    <?= date('M j, Y', strtotime($outcome['created_at'])) ?>
                                </span>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <div class="d-flex gap-md justify-content-center">
                                    <a
                                        href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/' . $outcome['id'] . '/edit') ?>"
                                        class="btn btn-primary"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Edit Outcome"
                                    >
                                        ✏️ Edit
                                    </a>
                                    <button
                                        onclick="showDeleteModal(<?= $outcome['id'] ?>, '<?= esc($outcome['outcome_text']) ?>')"
                                        class="btn btn-danger"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Delete Outcome"
                                    >
                                        🗑️ Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🎯</div>
            <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Project Outcomes Yet</h3>
            <p style="margin-bottom: var(--spacing-lg);">Define measurable project deliverables to track success metrics.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/create') ?>" class="btn btn-primary">
                🎯 Add First Outcome
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; padding: var(--spacing-xl); border-radius: var(--radius-md); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Delete Project Outcome</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete outcome "<span id="deleteOutcomeText"></span>"?
            This action cannot be undone.
        </p>

        <form id="deleteForm" method="post" style="display: none;">
            <?= csrf_field() ?>
        </form>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Outcome</button>
        </div>
    </div>
</div>

<script>
let currentDeleteOutcomeId = null;

function showDeleteModal(outcomeId, outcomeText) {
    currentDeleteOutcomeId = outcomeId;
    document.getElementById('deleteOutcomeText').textContent = outcomeText;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    currentDeleteOutcomeId = null;
}

function confirmDelete() {
    if (currentDeleteOutcomeId) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('admin/projects/' . $project['id'] . '/outcomes/') ?>' + currentDeleteOutcomeId + '/delete';
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
