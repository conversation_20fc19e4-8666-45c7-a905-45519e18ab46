<?php

namespace App\Models;

/**
 * Contractor Contact Model
 * 
 * Manages contractor contact persons including
 * primary contacts, positions, and contact information.
 */
class ContractorContactModel extends BaseModel
{
    protected $table      = 'contractor_contacts';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'contractor_id', 'name', 'position', 'email', 'phone',
        'is_primary', 'notes', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'contractor_id' => 'required|integer',
        'name'          => 'required|max_length[100]',
        'position'      => 'max_length[100]',
        'email'         => 'valid_email|max_length[100]',
        'phone'         => 'max_length[30]',
        'is_primary'    => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'contractor_id' => [
            'required' => 'Contractor ID is required'
        ],
        'name' => [
            'required' => 'Contact name is required'
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address'
        ]
    ];
    
    /**
     * Get contacts by contractor
     */
    public function getByContractor(int $contractorId): array
    {
        return $this->where('contractor_id', $contractorId)
                   ->orderBy('is_primary', 'DESC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get primary contact for contractor
     */
    public function getPrimaryContact(int $contractorId): ?array
    {
        return $this->where('contractor_id', $contractorId)
                   ->where('is_primary', 1)
                   ->first();
    }
    
    /**
     * Set primary contact
     */
    public function setPrimaryContact(int $contractorId, int $contactId): bool
    {
        // First, unset all primary contacts for this contractor
        $this->where('contractor_id', $contractorId)
             ->set('is_primary', 0)
             ->update();
        
        // Then set the specified contact as primary
        return $this->update($contactId, ['is_primary' => 1]);
    }
    
    /**
     * Get contacts with email addresses
     */
    public function getContactsWithEmail(int $contractorId): array
    {
        return $this->where('contractor_id', $contractorId)
                   ->where('email IS NOT NULL')
                   ->where('email !=', '')
                   ->orderBy('is_primary', 'DESC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get contacts with phone numbers
     */
    public function getContactsWithPhone(int $contractorId): array
    {
        return $this->where('contractor_id', $contractorId)
                   ->where('phone IS NOT NULL')
                   ->where('phone !=', '')
                   ->orderBy('is_primary', 'DESC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Search contacts across organization
     */
    public function searchContacts(int $orgId, string $search): array
    {
        return $this->select('contractor_contacts.*, contractors.name as contractor_name, contractors.contractor_code')
                   ->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                   ->where('contractors.org_id', $orgId)
                   ->groupStart()
                       ->like('contractor_contacts.name', $search)
                       ->orLike('contractor_contacts.email', $search)
                       ->orLike('contractor_contacts.phone', $search)
                       ->orLike('contractor_contacts.position', $search)
                   ->groupEnd()
                   ->orderBy('contractors.name', 'ASC')
                   ->orderBy('contractor_contacts.is_primary', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get contact statistics by organization
     */
    public function getContactStatistics(int $orgId): array
    {
        $stats = [];
        
        // Total contacts
        $stats['total'] = $this->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                              ->where('contractors.org_id', $orgId)
                              ->countAllResults();
        
        // Contacts with email
        $stats['with_email'] = $this->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                                   ->where('contractors.org_id', $orgId)
                                   ->where('contractor_contacts.email IS NOT NULL')
                                   ->where('contractor_contacts.email !=', '')
                                   ->countAllResults();
        
        // Contacts with phone
        $stats['with_phone'] = $this->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                                   ->where('contractors.org_id', $orgId)
                                   ->where('contractor_contacts.phone IS NOT NULL')
                                   ->where('contractor_contacts.phone !=', '')
                                   ->countAllResults();
        
        // Primary contacts
        $stats['primary'] = $this->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                                ->where('contractors.org_id', $orgId)
                                ->where('contractor_contacts.is_primary', 1)
                                ->countAllResults();
        
        // Most common positions
        $positionCounts = $this->select('position, COUNT(*) as count')
                              ->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                              ->where('contractors.org_id', $orgId)
                              ->where('position IS NOT NULL')
                              ->where('position !=', '')
                              ->groupBy('position')
                              ->orderBy('count', 'DESC')
                              ->limit(10)
                              ->findAll();
        
        $stats['common_positions'] = $positionCounts;
        
        return $stats;
    }
    
    /**
     * Get contractors without primary contact
     */
    public function getContractorsWithoutPrimaryContact(int $orgId): array
    {
        $contractorsWithPrimary = $this->select('DISTINCT contractor_id')
                                      ->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                                      ->where('contractors.org_id', $orgId)
                                      ->where('contractor_contacts.is_primary', 1)
                                      ->findAll();
        
        $contractorIds = array_column($contractorsWithPrimary, 'contractor_id');
        
        $contractorModel = new ContractorModel();
        $query = $contractorModel->where('org_id', $orgId);
        
        if (!empty($contractorIds)) {
            $query = $query->whereNotIn('id', $contractorIds);
        }
        
        return $query->findAll();
    }
    
    /**
     * Get all contacts for organization with contractor details
     */
    public function getOrganizationContacts(int $orgId): array
    {
        return $this->select('contractor_contacts.*, contractors.name as contractor_name, contractors.contractor_code, contractors.status as contractor_status')
                   ->join('contractors', 'contractors.id = contractor_contacts.contractor_id')
                   ->where('contractors.org_id', $orgId)
                   ->orderBy('contractors.name', 'ASC')
                   ->orderBy('contractor_contacts.is_primary', 'DESC')
                   ->orderBy('contractor_contacts.name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get contact directory for organization
     */
    public function getContactDirectory(int $orgId): array
    {
        $contacts = $this->getOrganizationContacts($orgId);
        
        $directory = [];
        foreach ($contacts as $contact) {
            $contractorId = $contact['contractor_id'];
            
            if (!isset($directory[$contractorId])) {
                $directory[$contractorId] = [
                    'contractor_name' => $contact['contractor_name'],
                    'contractor_code' => $contact['contractor_code'],
                    'contractor_status' => $contact['contractor_status'],
                    'contacts' => []
                ];
            }
            
            $directory[$contractorId]['contacts'][] = [
                'id' => $contact['id'],
                'name' => $contact['name'],
                'position' => $contact['position'],
                'email' => $contact['email'],
                'phone' => $contact['phone'],
                'is_primary' => $contact['is_primary'],
                'notes' => $contact['notes']
            ];
        }
        
        return $directory;
    }
    
    /**
     * Validate contact uniqueness within contractor
     */
    public function isContactUnique(int $contractorId, string $email, ?int $excludeId = null): bool
    {
        $query = $this->where('contractor_id', $contractorId)
                     ->where('email', $email);
        
        if ($excludeId) {
            $query = $query->where('id !=', $excludeId);
        }
        
        return $query->countAllResults() === 0;
    }
    
    /**
     * Get contact summary for contractor
     */
    public function getContactSummary(int $contractorId): array
    {
        $contacts = $this->getByContractor($contractorId);
        
        return [
            'total_contacts' => count($contacts),
            'primary_contact' => $this->getPrimaryContact($contractorId),
            'contacts_with_email' => count($this->getContactsWithEmail($contractorId)),
            'contacts_with_phone' => count($this->getContactsWithPhone($contractorId)),
            'all_contacts' => $contacts
        ];
    }
}
