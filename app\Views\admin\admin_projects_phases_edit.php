<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-phases" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Phase
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Editing phase: <strong><?= esc($phase['title']) ?></strong> in project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Phase Form -->
<div class="card">
    <div class="card-header">
        📋 Phase Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/phases/' . $phase['id'] . '/edit') ?>" class="phase-edit-form">
            <?= csrf_field() ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Phase Code -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="phase_code" class="form-label">
                            Phase Code <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text"
                               id="phase_code"
                               name="phase_code"
                               class="form-input"
                               value="<?= old('phase_code', $phase['phase_code']) ?>"
                               placeholder="e.g., PH001, INIT, PLAN"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Unique identifier for this phase (max 20 characters)
                        </small>
                    </div>

                    <!-- Phase Title -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="title" class="form-label">
                            Phase Title <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text"
                               id="title"
                               name="title"
                               class="form-input"
                               value="<?= old('title', $phase['title']) ?>"
                               placeholder="e.g., Planning Phase, Implementation Phase"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Descriptive name for this phase (max 150 characters)
                        </small>
                    </div>

                    <!-- Status -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="status" class="form-label">
                            Status <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="status"
                                name="status"
                                class="form-input"
                                required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status', $phase['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="deactivated" <?= old('status', $phase['status']) === 'deactivated' ? 'selected' : '' ?>>Deactivated</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current status of this phase
                        </small>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Start Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="start_date" class="form-label">
                            Start Date
                        </label>
                        <input type="date"
                               id="start_date"
                               name="start_date"
                               class="form-input"
                               value="<?= old('start_date', $phase['start_date']) ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            When this phase is scheduled to start
                        </small>
                    </div>

                    <!-- End Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="end_date" class="form-label">
                            End Date
                        </label>
                        <input type="date"
                               id="end_date"
                               name="end_date"
                               class="form-input"
                               value="<?= old('end_date', $phase['end_date']) ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            When this phase is scheduled to end
                        </small>
                    </div>

                    <!-- Phase Metadata -->
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-md);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            Phase Metadata
                        </h4>
                        <div style="display: grid; grid-template-columns: auto 1fr; gap: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                            <span style="color: var(--text-muted);">Sort Order:</span>
                            <span style="color: var(--text-secondary);"><?= esc($phase['sort_order']) ?></span>
                            
                            <span style="color: var(--text-muted);">Created:</span>
                            <span style="color: var(--text-secondary);"><?= date('M j, Y', strtotime($phase['created_at'])) ?></span>
                            
                            <?php if ($phase['updated_at'] && $phase['updated_at'] !== $phase['created_at']): ?>
                                <span style="color: var(--text-muted);">Updated:</span>
                                <span style="color: var(--text-secondary);"><?= date('M j, Y', strtotime($phase['updated_at'])) ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="description" class="form-label">
                    Description
                </label>
                <textarea id="description"
                          name="description"
                          class="form-input"
                          rows="4"
                          placeholder="Detailed description of this phase, its objectives, and key activities..."><?= old('description', $phase['description']) ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Optional detailed description of the phase
                </small>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    ✅ Update Phase
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Form validation styling - matches project edit form */
.phase-edit-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.phase-edit-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.phase-edit-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .phase-edit-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
}
</style>

<?= $this->endSection() ?>
