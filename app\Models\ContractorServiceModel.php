<?php

namespace App\Models;

/**
 * Contractor Service Model
 * 
 * Manages contractor services and capabilities including
 * service categories, descriptions, and primary service designation.
 */
class ContractorServiceModel extends BaseModel
{
    protected $table      = 'contractor_services';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'contractor_id', 'service_category', 'service_name', 'description',
        'is_primary', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'contractor_id'    => 'required|integer',
        'service_category' => 'required|max_length[100]',
        'service_name'     => 'required|max_length[150]',
        'is_primary'       => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'contractor_id' => [
            'required' => 'Contractor ID is required'
        ],
        'service_category' => [
            'required' => 'Service category is required'
        ],
        'service_name' => [
            'required' => 'Service name is required'
        ]
    ];
    
    /**
     * Get services by contractor
     */
    public function getByContractor(int $contractorId): array
    {
        return $this->where('contractor_id', $contractorId)
                   ->orderBy('is_primary', 'DESC')
                   ->orderBy('service_category', 'ASC')
                   ->orderBy('service_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get primary services by contractor
     */
    public function getPrimaryServices(int $contractorId): array
    {
        return $this->where('contractor_id', $contractorId)
                   ->where('is_primary', 1)
                   ->orderBy('service_category', 'ASC')
                   ->orderBy('service_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get services by category
     */
    public function getByCategory(string $category, ?int $orgId = null): array
    {
        $query = $this->select('contractor_services.*, contractors.name as contractor_name, contractors.contractor_code')
                     ->join('contractors', 'contractors.id = contractor_services.contractor_id')
                     ->where('contractor_services.service_category', $category)
                     ->where('contractors.status', 'active');
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractors.name', 'ASC')
                    ->orderBy('contractor_services.service_name', 'ASC')
                    ->findAll();
    }
    
    /**
     * Search services
     */
    public function searchServices(string $search, ?int $orgId = null): array
    {
        $query = $this->select('contractor_services.*, contractors.name as contractor_name, contractors.contractor_code')
                     ->join('contractors', 'contractors.id = contractor_services.contractor_id')
                     ->where('contractors.status', 'active')
                     ->groupStart()
                         ->like('contractor_services.service_name', $search)
                         ->orLike('contractor_services.service_category', $search)
                         ->orLike('contractor_services.description', $search)
                     ->groupEnd();
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractors.name', 'ASC')
                    ->orderBy('contractor_services.service_name', 'ASC')
                    ->findAll();
    }
    
    /**
     * Set primary service
     */
    public function setPrimaryService(int $contractorId, int $serviceId): bool
    {
        // First, unset all primary services for this contractor
        $this->where('contractor_id', $contractorId)
             ->set('is_primary', 0)
             ->update();
        
        // Then set the specified service as primary
        return $this->update($serviceId, ['is_primary' => 1]);
    }
    
    /**
     * Get service categories for organization
     */
    public function getServiceCategories(int $orgId): array
    {
        return $this->select('DISTINCT service_category')
                   ->join('contractors', 'contractors.id = contractor_services.contractor_id')
                   ->where('contractors.org_id', $orgId)
                   ->orderBy('service_category', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get service statistics by organization
     */
    public function getServiceStatistics(int $orgId): array
    {
        $stats = [];
        
        // Total services
        $stats['total'] = $this->join('contractors', 'contractors.id = contractor_services.contractor_id')
                              ->where('contractors.org_id', $orgId)
                              ->countAllResults();
        
        // By category
        $categoryCounts = $this->select('service_category, COUNT(*) as count')
                              ->join('contractors', 'contractors.id = contractor_services.contractor_id')
                              ->where('contractors.org_id', $orgId)
                              ->groupBy('service_category')
                              ->orderBy('count', 'DESC')
                              ->findAll();
        
        $stats['by_category'] = array_column($categoryCounts, 'count', 'service_category');
        
        // Most common services
        $serviceCounts = $this->select('service_name, COUNT(*) as count')
                             ->join('contractors', 'contractors.id = contractor_services.contractor_id')
                             ->where('contractors.org_id', $orgId)
                             ->groupBy('service_name')
                             ->orderBy('count', 'DESC')
                             ->limit(10)
                             ->findAll();
        
        $stats['most_common'] = $serviceCounts;
        
        return $stats;
    }
    
    /**
     * Find contractors by service
     */
    public function findContractorsByService(string $serviceName, ?int $orgId = null): array
    {
        $query = $this->select('contractor_services.*, contractors.name as contractor_name, contractors.contractor_code, contractors.contact_person, contractors.contact_email, contractors.contact_phone')
                     ->join('contractors', 'contractors.id = contractor_services.contractor_id')
                     ->where('contractors.status', 'active')
                     ->like('contractor_services.service_name', $serviceName);
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractor_services.is_primary', 'DESC')
                    ->orderBy('contractors.name', 'ASC')
                    ->findAll();
    }
    
    /**
     * Get contractors with specific service category
     */
    public function getContractorsByCategory(string $category, ?int $orgId = null): array
    {
        $query = $this->select('DISTINCT contractors.id, contractors.name, contractors.contractor_code, contractors.contact_person, contractors.contact_email')
                     ->join('contractors', 'contractors.id = contractor_services.contractor_id')
                     ->where('contractors.status', 'active')
                     ->where('contractor_services.service_category', $category);
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractors.name', 'ASC')->findAll();
    }
    
    /**
     * Check if contractor has service
     */
    public function hasService(int $contractorId, string $serviceName): bool
    {
        return $this->where('contractor_id', $contractorId)
                   ->where('service_name', $serviceName)
                   ->countAllResults() > 0;
    }
    
    /**
     * Get service portfolio summary for contractor
     */
    public function getServicePortfolio(int $contractorId): array
    {
        $services = $this->getByContractor($contractorId);
        
        $portfolio = [
            'total_services' => count($services),
            'categories' => [],
            'primary_services' => [],
            'all_services' => $services
        ];
        
        foreach ($services as $service) {
            // Group by category
            if (!isset($portfolio['categories'][$service['service_category']])) {
                $portfolio['categories'][$service['service_category']] = [];
            }
            $portfolio['categories'][$service['service_category']][] = $service;
            
            // Collect primary services
            if ($service['is_primary']) {
                $portfolio['primary_services'][] = $service;
            }
        }
        
        return $portfolio;
    }
}
